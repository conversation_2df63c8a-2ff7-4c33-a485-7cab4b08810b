<template>
    <!-- iframe 查看器 -->
    <IframeViewer
        v-if="showIframe"
        :url="currentArticleUrl"
        :title="currentArticleTitle"
        @back="handleIframeBack"
        @load="handleIframeLoad"
        @error="handleIframeError"
    />

    <!-- 新闻页面内容 -->
    <div v-else class="news-page">
        <!-- 加载状态 -->
        <div v-if="loading" class="loading-container">
            <div class="loading-spinner"></div>
            <p class="loading-text">正在获取最新AI资讯...</p>
        </div>

        <!-- 错误状态 -->
        <div v-else-if="error && articles.length === 0" class="error-container">
            <div class="error-icon">⚠️</div>
            <h3 class="error-title">获取资讯失败</h3>
            <p class="error-message">{{ error }}</p>
            <button class="retry-btn" @click="loadNews">重试</button>
        </div>

        <!-- 正常内容 -->
        <template v-else>
            <!-- 资讯列表 -->
            <div class="news-list">
                <div
                    v-for="article in articles"
                    :key="article.id"
                    class="news-card"
                    @click="openArticle(article.url, article.title)"
                >
                    <div class="article-content">
                        <div class="article-meta">
                            <span class="article-source">{{ article.source }}</span>
                        </div>
                        <h3 class="article-title">{{ article.title }}</h3>
                    </div>
                </div>
            </div>
        </template>

        <!-- 悬浮刷新按钮 -->
        <button
            class="floating-refresh-btn"
            :class="{ 'is-loading': loading }"
            @click="loadNews"
            :disabled="loading"
            :title="loading ? '更新中...' : '刷新资讯'"
        >
            <i class="pi pi-refresh refresh-icon"></i>
        </button>
    </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import newsService from '@/services/newsService';
import IframeViewer from '@/views/smart-trainer/pages/learn/components/IframeViewer.vue';

// 响应式状态
const articles = ref([]);
const loading = ref(false);
const error = ref('');

// iframe 相关状态
const showIframe = ref(false);
const currentArticleUrl = ref('');
const currentArticleTitle = ref('');

/**
 * 加载新闻数据
 */
const loadNews = async () => {
    try {
        loading.value = true;
        error.value = '';

        const newsData = await newsService.getAINews();
        articles.value = newsData || [];

        if (articles.value.length === 0) {
            error.value = '暂无AI相关资讯，请稍后再试';
        }
    } catch (err) {
        console.error('加载新闻失败:', err);
        error.value = err.message || '网络连接失败，请检查网络设置';
    } finally {
        loading.value = false;
    }
};

/**
 * 打开文章链接
 * @param {string} url - 文章URL
 * @param {string} title - 文章标题
 */
const openArticle = (url, title) => {
    if (!url || url === '#') {
        console.warn('文章链接无效:', url);
        return;
    }

    try {
        // 验证URL格式
        new URL(url);

        // 设置 iframe 相关数据并显示
        currentArticleUrl.value = url;
        currentArticleTitle.value = title || '新闻详情';
        showIframe.value = true;
    } catch (err) {
        console.error('无效的文章链接:', url, err);
        // 如果URL无效，降级到新窗口打开
        window.open(url, '_blank', 'noopener,noreferrer');
    }
};

/**
 * 处理 iframe 返回事件
 */
const handleIframeBack = () => {
    showIframe.value = false;
    currentArticleUrl.value = '';
    currentArticleTitle.value = '';
};

/**
 * 处理 iframe 加载完成事件
 */
const handleIframeLoad = () => {
    console.log('新闻 iframe 加载完成');
};

/**
 * 处理 iframe 加载错误事件
 */
const handleIframeError = errorInfo => {
    console.error('新闻 iframe 加载错误:', errorInfo);
    // 可以在这里添加错误处理逻辑，比如显示错误提示或降级到新窗口打开
};

// 生命周期钩子
onMounted(() => {
    loadNews();
});
</script>

<style lang="scss" scoped>
.news-page {
    padding: 12px;
    height: 100%;
    display: flex;
    flex-direction: column;
    flex-shrink: 0;
    overflow: hidden;
    position: relative;
}

.loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 200px;
    color: #666;

    .loading-spinner {
        width: 40px;
        height: 40px;
        border: 3px solid #e5e5ea;
        border-top: 3px solid #007aff;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin-bottom: 16px;
    }

    .loading-text {
        font-size: 14px;
        margin: 0;
    }
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

.error-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 200px;
    text-align: center;
    color: #666;

    .error-icon {
        font-size: 48px;
        margin-bottom: 16px;
    }

    .error-title {
        font-size: 18px;
        font-weight: 600;
        color: #000;
        margin: 0 0 8px 0;
    }

    .error-message {
        font-size: 14px;
        margin: 0 0 20px 0;
        max-width: 300px;
    }

    .retry-btn {
        background: #007aff;
        color: white;
        border: none;
        padding: 10px 20px;
        border-radius: 18px;
        font-size: 14px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;

        &:hover {
            background: #0056d0;
        }
    }
}

.news-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
    flex: 1;
    overflow-y: auto;
    scrollbar-width: none;
    -ms-overflow-style: none;
    &::-webkit-scrollbar {
        display: none;
    }
    -webkit-overflow-scrolling: touch;
}

.news-card {
    background: #f2f2f7;
    border-radius: 10px;
    padding: 16px;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 1px solid #e5e5e7;

    &:hover {
        border-color: #007aff;

        .article-title {
            color: #007aff;
        }
    }

    .article-content {
        .article-meta {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 8px;

            .article-source {
                background: #34c759;
                color: white;
                padding: 2px 8px;
                border-radius: 4px;
                font-size: 11px;
                font-weight: 600;
            }

            .article-date {
                color: #999;
                font-size: 12px;
            }
        }

        .article-title {
            font-size: 16px;
            font-weight: 600;
            color: #000;
            line-height: 1.4;
            transition: color 0.2s ease;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }
    }
}

// 悬浮刷新按钮
.floating-refresh-btn {
    position: fixed;
    right: 20px;
    bottom: 80px;
    width: 56px;
    height: 56px;
    border-radius: 50%;
    background: linear-gradient(135deg, #007aff 0%, #0056d0 100%);
    border: none;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 20px rgba(0, 122, 255, 0.3);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    z-index: 1000;

    .refresh-icon {
        color: #fff;
        font-size: 24px;
        transition: transform 0.3s ease;
    }

    &.is-loading {
        pointer-events: none;

        .refresh-icon {
            animation: spin 1s linear infinite;
        }
    }

    &:disabled {
        opacity: 0.7;
        cursor: not-allowed;
        transform: none;
    }
}

@media (max-width: 480px) {
    .news-page {
        padding: 8px;
    }

    .floating-refresh-btn {
        right: 16px;
        bottom: 80px;
        width: 50px;
        height: 50px;

        .refresh-icon {
            font-size: 20px;
        }
    }

    .news-card {
        padding: 12px;

        .article-content .article-title {
            font-size: 14px;
        }
    }
}
</style>
