/**
 * 新闻API服务 - 简化版
 * 获取AI相关资讯
 */

// 支持的平台配置 - 按照API支持的完整平台列表
const PLATFORMS = [
    { key: 'baidu', name: '百度' },
    { key: 'shaoshupai', name: '少数派' },
    // { key: 'weibo', name: '微博' },
    { key: 'zhihu', name: '知乎' },
    { key: '36kr', name: '36氪' },
    // { key: '52pojie', name: '吾爱破解' },
    // { key: 'bilibili', name: '哔哩哔哩' },
    // { key: 'douban', name: '豆瓣' },
    // { key: 'hupu', name: '虎扑' },
    // { key: 'tieba', name: '贴吧' },
    { key: 'juejin', name: '掘金' },
    // { key: 'douyin', name: '抖音' },
    { key: 'v2ex', name: 'V2EX' },
    // { key: 'jinritouti<PERSON>', name: '今日头条' },
    { key: 'tenxunwang', name: '腾讯网' }
    // { key: 'stackoverflow', name: 'Stack Overflow' },
    // { key: 'github', name: 'Git<PERSON><PERSON>' },
    // { key: 'hackernews', name: 'Hacker News' }
];

// 平台循环状态存储键
const PLATFORM_INDEX_KEY = 'news_platform_index';

// AI相关关键词（精简版）
const CAR_KEYWORDS = [
    // === 基础车辆类型 ===
    '汽车',
    '新能源汽车',
    '电动汽车',
    '燃油车',
    '混动',
    '新车',
    '车型',
    '汽车品牌',
    '混合动力',
    '纯电动',
    '插电混动',
    '增程式',
    '氢燃料电池',
    'SUV',
    '轿车',
    '跑车',
    '皮卡',
    'MPV',
    '微型车',
    '小型车',
    '紧凑型车',
    '中型车',
    '大型车',

    // === 汽车品牌 ===
    '特斯拉',
    '比亚迪',
    '蔚来',
    '小鹏',
    '理想',
    '奔驰',
    '宝马',
    '奥迪',
    '丰田',
    '本田',
    '大众',
    '吉利',
    '长城',
    '红旗',
    '小米',
    '华为',
    '极氪',
    '岚图',
    '问界',
    '阿维塔',
    '智己',
    '飞凡',

    // === 核心技术系统 ===
    '自动驾驶',
    '智能驾驶',
    '车联网',
    '汽车科技',
    '新能源',
    '充电桩',
    '电池技术',
    '发动机',
    '变速箱',
    '悬架',
    '制动系统',
    '刹车系统',
    '转向系统',
    '传动系统',
    '冷却系统',
    '排气系统',
    '燃油系统',
    '电气系统',
    '空调系统',
    '安全气囊',
    'ABS',
    'ESP',
    'EBD',

    // === 动力与能源 ===
    '电机',
    '电控',
    '电池',
    '锂电池',
    '磷酸铁锂',
    '三元锂',
    '固态电池',
    '超充',
    '快充',
    '慢充',
    '换电',
    '续航',
    '里程',
    '能耗',
    '功率',
    '扭矩',
    '马力',
    '涡轮增压',
    '自然吸气',

    // === 智能化技术 ===
    '激光雷达',
    '毫米波雷达',
    '摄像头',
    '传感器',
    '芯片',
    '算力',
    'OTA',
    '车机',
    '中控屏',
    'HUD',
    '语音识别',
    '人机交互',
    '座舱',
    '智能座舱',
    '辅助驾驶',
    'L2',
    'L3',
    'L4',
    'L5',

    // === 汽车服务与产业 ===
    '汽车销量',
    '车展',
    '上市',
    '召回',
    '保养',
    '维修',
    '保险',
    '二手车',
    '车贷',
    '租车',
    '共享汽车',
    '汽车金融',
    '汽车后市场',
    '4S店',
    '经销商',

    // === 政策与标准 ===
    '国六',
    '双积分',
    '补贴',
    '购置税',
    '车牌',
    '限行',
    '环保',
    '排放',
    '碳中和',
    '绿牌',
    '蓝牌'
];

class NewsService {
    constructor() {
        this.baseUrl = 'https://orz.ai/api/v1/dailynews';
    }

    /**
     * 获取指定平台的新闻数据
     * @param {string} platform - 平台标识
     * @returns {Promise<Array>} 新闻文章数组
     */
    async fetchPlatformNews(platform) {
        try {
            const response = await fetch(`${this.baseUrl}/?platform=${platform}`, {
                method: 'GET',
                headers: { 'Content-Type': 'application/json' },
                signal: AbortSignal.timeout(10000)
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}`);
            }

            const data = await response.json();

            if (data.status === '200' && data.data) {
                return this.transformNewsData(data.data, platform);
            }
            return [];
        } catch (error) {
            console.error(`获取 ${platform} 新闻失败:`, error);
            return [];
        }
    }

    /**
     * 转换新闻数据格式
     * @param {Array} rawData - 原始数据
     * @param {string} platform - 平台标识
     * @returns {Array} 格式化的新闻数据
     */
    transformNewsData(rawData, platform) {
        const platformConfig = PLATFORMS.find(p => p.key === platform);

        return rawData.map((item, index) => ({
            id: `${platform}_${Date.now()}_${index}`,
            title: item.title || '无标题',
            url: item.url || '#',
            date: item.publish_time || new Date(),
            source: platformConfig?.name || platform
        }));
    }

    /**
     * 过滤包含汽车关键词的文章
     * @param {Array} articles - 文章数组
     * @returns {Array} 过滤后的文章数组
     */
    filterCarRelatedArticles(articles) {
        return articles.filter(article => {
            const title = (article.title || '').toLowerCase();
            const content = (article.content || '').toLowerCase();

            // 检查标题是否包含任何汽车关键词
            return CAR_KEYWORDS.some(keyword => {
                const keywordLower = keyword.toLowerCase();
                return title.includes(keywordLower) || content.includes(keywordLower);
            });
        });
    }

    /**
     * 获取当前平台循环索引
     * @returns {number} 当前索引位置
     */
    getCurrentPlatformIndex() {
        try {
            const stored = localStorage.getItem(PLATFORM_INDEX_KEY);
            return stored ? parseInt(stored, 10) : 0;
        } catch (error) {
            console.error('获取平台索引失败:', error);
            return 0;
        }
    }

    /**
     * 更新平台循环索引
     * @param {number} index - 新的索引位置
     */
    updatePlatformIndex(index) {
        try {
            localStorage.setItem(PLATFORM_INDEX_KEY, index.toString());
        } catch (error) {
            console.error('更新平台索引失败:', error);
        }
    }

    /**
     * 获取下一批要请求的平台（3个）
     * @returns {Array} 平台配置数组
     */
    getNextPlatforms() {
        const currentIndex = this.getCurrentPlatformIndex();
        const platforms = [];

        // 获取3个平台，循环处理
        for (let i = 0; i < 3; i++) {
            const index = (currentIndex + i) % PLATFORMS.length;
            platforms.push(PLATFORMS[index]);
        }

        // 更新索引，下次从第4个平台开始
        const nextIndex = (currentIndex + 3) % PLATFORMS.length;
        this.updatePlatformIndex(nextIndex);

        return platforms;
    }

    /**
     * 获取汽车新闻
     * @returns {Promise<Array>} 汽车新闻文章数组
     */
    async getAINews() {
        try {
            // 获取本次要请求的3个平台
            const selectedPlatforms = this.getNextPlatforms();

            // 并发获取选中平台的数据
            const promises = selectedPlatforms.map(platform =>
                this.fetchPlatformNews(platform.key)
            );
            const results = await Promise.allSettled(promises);

            // 合并所有成功的结果
            let allArticles = [];
            results.forEach((result, index) => {
                if (result.status === 'fulfilled' && result.value) {
                    allArticles = allArticles.concat(result.value);
                } else {
                    console.error(`${selectedPlatforms[index].name} 获取失败:`, result.reason);
                }
            });

            // 过滤包含汽车关键词的文章
            const filteredArticles = this.filterCarRelatedArticles(allArticles);

            // 按时间排序，限制数量
            const finalArticles = filteredArticles.sort((a, b) => b.date - a.date);

            return finalArticles;
        } catch (error) {
            console.error('获取汽车新闻失败:', error);
            return [];
        }
    }

    /**
     * 重置平台循环索引
     */
    resetPlatformIndex() {
        try {
            localStorage.removeItem(PLATFORM_INDEX_KEY);
        } catch (error) {
            console.error('重置平台索引失败:', error);
        }
    }
}

// 创建单例实例
const newsService = new NewsService();
export default newsService;
