<template>
    <div v-if="visible" class="explanation-modal-overlay" @click="handleOverlayClick">
        <div class="explanation-modal" @click.stop>
            <!-- 关闭按钮 -->
            <button class="close-btn" @click="closeModal">
                <i class="pi pi-times"></i>
            </button>

            <!-- 答错提示头部 -->
            <div class="error-header">
                <div class="error-icon">
                    <i class="pi pi-times-circle"></i>
                </div>
                <div class="error-text">
                    <h3>答题错误</h3>
                    <p>别灰心，看看正确答案和解析吧</p>
                </div>
            </div>

            <!-- 内容区域 -->
            <div class="modal-content">
                <!-- 答案对比区域 -->
                <div class="answers-comparison">
                    <!-- 用户选择的答案 -->
                    <div class="answer-card wrong-answer">
                        <div class="card-header">
                            <i class="pi pi-user"></i>
                            <span>你的答案</span>
                        </div>
                        <div class="card-content">
                            <template v-if="questionType === 'single' || questionType === 'multi'">
                                <div
                                    v-for="option in userSelectedOptions"
                                    :key="option.key"
                                    class="answer-item"
                                >
                                    <span class="option-key">{{ option.key }}.</span>
                                    <span class="option-text">{{ option.text }}</span>
                                </div>
                            </template>
                            <template v-else-if="questionType === 'true_false'">
                                <div class="answer-item">
                                    <i :class="userAnswer ? 'pi pi-check' : 'pi pi-times'"></i>
                                    <span>{{ userAnswer ? '正确' : '错误' }}</span>
                                </div>
                            </template>
                        </div>
                    </div>

                    <!-- 正确答案 -->
                    <div class="answer-card correct-answer">
                        <div class="card-header">
                            <i class="pi pi-check"></i>
                            <span>正确答案</span>
                        </div>
                        <div class="card-content">
                            <template v-if="questionType === 'single' || questionType === 'multi'">
                                <div
                                    v-for="option in correctOptions"
                                    :key="option.key"
                                    class="answer-item"
                                >
                                    <span class="option-key">{{ option.key }}.</span>
                                    <span class="option-text">{{ option.text }}</span>
                                </div>
                            </template>
                            <template v-else-if="questionType === 'true_false'">
                                <div class="answer-item">
                                    <i :class="correctAnswer ? 'pi pi-check' : 'pi pi-times'"></i>
                                    <span>{{ correctAnswer ? '正确' : '错误' }}</span>
                                </div>
                            </template>
                        </div>
                    </div>
                </div>

                <!-- 解析说明 -->
                <div class="explanation-section">
                    <div class="explanation-header">
                        <i class="pi pi-lightbulb"></i>
                        <span>解析</span>
                    </div>
                    <div class="explanation-content">
                        {{ explanation }}
                    </div>
                </div>
            </div>

            <!-- 底部按钮 -->
            <div class="modal-footer">
                <button class="next-btn" @click="handleNext">
                    {{ isLastQuestion ? '查看结果' : '下一题' }}
                </button>
            </div>
        </div>
    </div>
</template>

<script setup>
import { computed } from 'vue';

// Props 定义
const props = defineProps({
    // 是否显示弹窗
    visible: {
        type: Boolean,
        default: false
    },
    // 题目类型
    questionType: {
        type: String,
        required: true
    },
    // 用户答案
    userAnswer: {
        required: true
    },
    // 正确答案
    correctAnswer: {
        required: true
    },
    // 选项列表
    options: {
        type: Array,
        default: () => []
    },
    // 解析说明
    explanation: {
        type: String,
        default: ''
    },
    // 是否为最后一题
    isLastQuestion: {
        type: Boolean,
        default: false
    }
});

// 事件定义
const emit = defineEmits(['close', 'next']);

// 计算用户选择的选项
const userSelectedOptions = computed(() => {
    if (props.questionType === 'true_false') {
        return [];
    }

    const userAnswerArray = Array.isArray(props.userAnswer) ? props.userAnswer : [props.userAnswer];
    return props.options.filter(option => userAnswerArray.includes(option.key));
});

// 计算正确选项
const correctOptions = computed(() => {
    if (props.questionType === 'true_false') {
        return [];
    }

    const correctAnswerArray = Array.isArray(props.correctAnswer)
        ? props.correctAnswer
        : [props.correctAnswer];
    return props.options.filter(option => correctAnswerArray.includes(option.key));
});

/**
 * 关闭弹窗
 */
const closeModal = () => {
    emit('close');
};

/**
 * 点击遮罩层关闭弹窗
 */
const handleOverlayClick = () => {
    closeModal();
};

/**
 * 处理下一题或查看结果
 */
const handleNext = () => {
    emit('next');
};
</script>

<style lang="scss" scoped>
.explanation-modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.4);
    display: flex;
    align-items: flex-end;
    justify-content: center;
    z-index: 1000;
    transition: opacity 0.2s ease;
}

.explanation-modal {
    background: white;
    border-radius: 16px 16px 0 0;
    width: 100%;
    max-width: 500px;
    max-height: 85vh;
    overflow-y: auto;
    position: relative;
    transform: translateY(0);
    transition: transform 0.2s ease;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.close-btn {
    position: absolute;
    top: 12px;
    right: 16px;
    width: 28px;
    height: 28px;
    border: none;
    background: rgba(0, 0, 0, 0.05);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    color: #666;
    z-index: 1;
    transition: background-color 0.2s ease;

    &:hover {
        background: rgba(0, 0, 0, 0.1);
    }

    i {
        font-size: 12px;
    }
}

.error-header {
    display: flex;
    align-items: center;
    gap: 16px;
    padding: 24px 20px 20px;
    background: linear-gradient(135deg, #fff1f0 0%, #fff2f0 100%);
    border-bottom: 1px solid #ffe7e6;
    flex-shrink: 0;

    .error-icon {
        width: 48px;
        height: 48px;
        background: #ff4d4f;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 20px;
    }

    .error-text {
        flex: 1;

        h3 {
            margin: 0 0 4px 0;
            font-size: 18px;
            font-weight: 600;
            color: #cf1322;
        }

        p {
            margin: 0;
            font-size: 14px;
            color: #8c8c8c;
            line-height: 1.4;
        }
    }
}

.modal-content {
    padding: 20px;
}

.answers-comparison {
    display: flex;
    flex-direction: column;
    gap: 16px;
    margin-bottom: 24px;
}

.answer-card {
    border-radius: 12px;
    overflow: hidden;
    border: 1px solid;

    &.wrong-answer {
        border-color: #ff4d4f;
        background: #fff2f0;

        .card-header {
            background: #ff4d4f;
            color: white;
        }
    }

    &.correct-answer {
        border-color: #52c41a;
        background: #f6ffed;

        .card-header {
            background: #52c41a;
            color: white;
        }
    }
}

.card-header {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 16px;
    font-size: 14px;
    font-weight: 600;

    i {
        font-size: 16px;
    }
}

.card-content {
    padding: 16px;
}

.answer-item {
    display: flex;
    align-items: flex-start;
    gap: 8px;
    margin-bottom: 8px;
    font-size: 14px;
    line-height: 1.5;

    &:last-child {
        margin-bottom: 0;
    }

    .option-key {
        font-weight: 600;
        min-width: 20px;
        color: #000;
    }

    .option-text {
        flex: 1;
        font-weight: 600;
    }

    i {
        margin-top: 2px;
        font-size: 16px;
    }
}

.explanation-section {
    .explanation-header {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-bottom: 12px;
        font-size: 16px;
        font-weight: 600;
        color: #1890ff;

        i {
            font-size: 18px;
        }
    }

    .explanation-content {
        background: #f8f9fa;
        border: 1px solid #e9ecef;
        border-radius: 12px;
        padding: 12px;
        font-size: 14px;
        line-height: 1.6;
        color: #495057;
    }
}

.modal-footer {
    padding: 0 20px 24px;
    flex-shrink: 0;
}

.next-btn {
    width: 100%;
    height: 48px;
    background: #1890ff;
    color: white;
    border: none;
    border-radius: 12px;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.2s ease;

    &:hover {
        background: #40a9ff;
    }

    &:active {
        background: #096dd9;
    }
}

// 响应式设计
@media (max-width: 480px) {
    .error-header {
        padding: 20px 16px 16px;
        gap: 12px;

        .error-icon {
            width: 40px;
            height: 40px;
            font-size: 18px;
        }

        .error-text {
            h3 {
                font-size: 16px;
            }

            p {
                font-size: 14px;
            }
        }
    }

    .modal-content {
        padding: 16px;
        flex: 1;
        overflow-y: auto;
        -ms-overflow-style: none;
        scrollbar-width: none;
        &::-webkit-scrollbar {
            display: none;
        }
        -webkit-overflow-scrolling: touch; 
    }

    .card-header {
        padding: 10px 12px;
        font-size: 14px;
    }

    .card-content {
        padding: 12px;
    }

    .answer-item {
        font-size: 14px;
    }

    .explanation-content {
        padding: 12px;
        font-size: 14px;
    }

    .modal-footer {
        padding: 0 16px 10px;
    }

    .next-btn {
        height: 44px;
        font-size: 15px;
    }
}
</style>
