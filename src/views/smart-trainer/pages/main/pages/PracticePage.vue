<template>
    <div class="practice-page">
        <div class="development-notice">
            <div class="icon-container">
                <img src="https://z.autoimg.cn/dealer_microfe_aidev/assets/chat-knowledge-h5/smart-trainer/main/later5.png" alt="" sizes="" srcset="">
            </div>
            <div class="text-content">
                <div class="title">功能开发中....</div>
            </div>
        </div>
    </div>
</template>

<script setup>
// 暂无需要的逻辑处理
</script>

<style lang="scss" scoped>
.practice-page {
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
}

.development-notice {
    text-align: center;
    width: 100%;
    height: 100%;

    .icon-container {
        margin-bottom: 24px;
    }

    .text-content {
        .title {
            font-size: 16px;
            font-weight: 600;
            color: #333;
            margin-bottom: 12px;
            line-height: 1.4;
        }
    }
}
</style>
